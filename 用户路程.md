第二部分：用户旅程与系统能力
本部分将抽象的架构转化为具体的工作流程，详细说明不同角色的用户将如何与系统互动以实现他们的目标。

2.1 用户画像深度剖析：数据生产者与数据消费者
为了设计出贴合实际需求的工作流和UI组件，我们必须首先清晰地理解系统的主要用户、他们的动机以及他们的需求。我们将借鉴成熟的MLOps用户画像定义  来构建我们的用户模型。   

画像一：数据工程师 (数据生产者)

角色: 负责构建和维护稳健、可扩展的数据管道。他们获取原始数据，进行清洗、转换，并将其提供给下游用户使用。

目标: 可靠性、可扩展性、数据质量和自动化。他们希望最大限度地减少手动干预，并确保数据管道是可观察和可维护的。

系统需求: 强大的命令行工具（CLI）/软件开发工具包（SDK）以支持自动化；清晰的用户界面（UI）用于配置和监控数据导入任务；稳健的错误处理和警报机制。

画像二：数据科学家 / ML工程师 (数据消费者)

角色: 使用数据来探索假设、训练模型和产生洞见。

目标: 快速迭代、可复现性，以及轻松获取高质量、文档齐全的数据。他们希望将更少的时间花在“数据整理”上，而将更多的时间用于建模。

系统需求: 直观的数据发现界面（搜索、浏览）；丰富的数据集文档（“数据集卡片”）；易于使用的数据预览工具；以及在他们偏好的开发环境（如Python、R）中，通过一行简单的命令就能加载特定数据集版本的能力。

2.2 数据工程师的旅程：数据导入与注册
本节将描绘数据工程师将新数据引入系统的端到端流程，涵盖从初始设置到持续监控的完整生命周期。

分步工作流:

定义目标: 数据工程师首先确定新数据集在命名空间中的位置（例如，raw_data.pos_terminals.transaction_logs）。如果 目录 或 模式 不存在，他们在权限允许的情况下创建它们。

配置数据源: 在UI中，数据工程师导航至“数据导入”功能。他们选择数据源类型（例如，“Amazon S3”）。

提供凭证与参数: 数据工程师填写一个针对所选源类型的表单。这是一个关键的UI组件，其设计必须兼顾清晰性和安全性。

配置导入任务: 数据工程师定义任务的具体行为：

调度: 一次性运行，或按周期性计划运行（例如，每天凌晨2点）。

同步行为: 全量刷新（覆盖）或增量追加。

Schema推断: 可选项，允许系统从源文件中自动检测数据结构。

失败策略: 作业失败时的处理方式（例如，向特定渠道发送警报）。

执行与监控: 数据工程师触发导入任务。系统提供一个仪表盘视图，显示所有导入任务的状态：运行中、成功、失败，以及历史运行记录。

自动注册: 任务成功完成后，系统会自动在目标 数据集 下创建一个新的 数据集版本，填充其元数据（schema、profile），并记录连接该版本与此次导入任务运行的血缘关系。

为了确保系统具有良好的可扩展性和一致的用户体验，为不同数据源设计标准化的配置界面至关重要。下面的表格为开发团队定义了数据源配置的契约，直接满足了用户对S3、Hive和监控系统数据导入的需求。

数据源类型	参数名称	UI控件	描述与示例	是否必须	来源参考
Amazon S3	connection_name	文本输入框	用户友好的连接名称。	是	
access_key_id	密码输入框	AWS访问密钥ID。	是	
secret_access_key	密码输入框	AWS秘密访问密钥。	是	
bucket_name	文本输入框	S3存储桶的名称。例如：my-raw-data-lake	是	
folder_path	文本输入框	存储桶内的特定文件夹路径。例如：transactions/2024/	否	
region	下拉菜单	存储桶所在的AWS区域。例如：us-east-1	否	
Apache Hive	connection_name	文本输入框	用户友好的连接名称。	是	
metastore_uri	文本输入框	Hive Metastore的Thrift URI。例如：thrift://hive-meta.service:9083	是	
database_name	文本输入框	Hive数据库的名称。	是	
table_name	文本输入框	要导入的源表的名称。	是	
监控流 (Kafka)	connection_name	文本输入框	用户友好的连接名称。	是	
bootstrap_servers	文本区域	Kafka Broker地址的逗号分隔列表。例如：kafka1:9092,kafka2:9092	是	
topic_name	文本输入框	要消费的Kafka主题。	是	
consumer_group_id	文本输入框	此导入作业的消费者组ID。	是	
data_format	下拉菜单	消息的数据格式（例如，JSON, Avro, Protobuf）。	是	-
  
2.3 数据科学家的旅程：发现、预览与消费
本节旨在为数据科学家创建一个无摩擦的工作流程，使他们能够轻松地发现、理解、信任并最终在他们首选的开发环境中使用数据集。

分步工作流:

发现 (Discovery): 数据科学家进入系统主页。他们既可以浏览 目录 的层级结构，也可以使用强大的搜索栏。他们输入“customer reviews”，并使用分面筛选器来过滤 modality:text 和 language:en 的数据集。

评估 (Evaluation): 搜索结果中出现一个名为 products.customer_feedback.reviews_en 的数据集。数据科学家点击进入数据集详情页。此页面是该数据集的中心枢纽，突出显示其“数据集卡片”（描述、所有者、使用注意事项）。所有可用版本及其别名（   

latest, production）的列表也清晰可见。

数据预览 (Data Preview): 数据科学家选择 production 版本。数据预览组件加载，显示数据的样本。这是建立信任最关键的一步。预览UI是模态感知（modality-aware）的，即它会根据数据类型（表格、图像、音频等）提供不同的预览体验。

消费 (Consumption): 在对数据感到满意后，数据科学家点击“使用”或“访问”按钮。一个弹窗出现，显示在各种语言中访问此特定数据集版本的代码片段。对于Python，它可能如下所示：

Python

import dataset_sdk
df = dataset_sdk.load(
    "products.customer_feedback.reviews_en:production"
)
这行简单的命令封装了所有复杂性，包括查找文件路径、处理凭证和下载数据 。SDK在后台处理这一切，将数据直接拉取到Pandas DataFrame、Spark DataFrame或其他相关格式中。   

为了构建一个高质量、有价值的数据预览功能，系统必须能够根据数据模态提供定制化的预览体验。一个通用的预览器价值有限。下表为开发团队提供了构建这一关键组件的明确规范。

数据模态	关键UI特性与控件	描述	来源参考
表格 (Tabular)	分页网格视图、列排序、基础筛选、列统计信息（最小/最大/平均值）、带固定表头的水平/垂直滚动	提供熟悉的电子表格般体验。排序和筛选对于快速探索至关重要。固定表头在滚动大型表格时保持上下文。	
图像 (Image)	缩略图库网格、带缩放/平移的单图查看器、“上一个/下一个”导航、显示图像元数据（尺寸、格式）	允许用户通过缩略图快速浏览图像集合，然后以高分辨率检查单个图像。图库模式是标准做法。	
音频 (Audio)	音频剪辑列表、波形可视化、播放/暂停/拖动控件、音量控制、显示元数据（采样率、时长、声道）	每个音频文件都需要自己的播放器。可视化波形有助于用户识别静音或高能量部分。基本的播放控制是必须的。	
文本 (Corpus)	文档列表、带搜索/高亮功能的全文查看器、上下文关联（KWIC）视图、元数据显示（词数、文档来源）	对于大型文本语料库，用户需要能够在文档中搜索关键词并在上下文中查看它们（上下文关联）。简单的文本转储是不够的。	
  
2.4 发布工作流：将数据集提升至生产状态
为了满足用户对“AI数据集发布”这一治理能力的需求，我们需要定义一个正式的、受控的流程，用于将数据集版本从开发状态提升到可信的、生产就绪的状态。这个流程的设计借鉴了软件工程中经过实战检验的“拉取请求”（Pull Request）模型，使其对于技术用户而言直观且可靠。

分步工作流:

发起请求 (Initiation): 一位数据工程师创建了一个新的 数据集版本（例如 v3.0.0），并认为它已准备好用于生产。他们导航到该数据集的详情页，并为这个版本发起一个“发布请求”。

自动化验证 (Automated Validation): 系统自动对候选版本运行一系列预先配置的检查：

Schema检查: 新版本的schema是否与前一个生产版本兼容？（例如，是否只增加了新列，而没有破坏性更改）。

数据质量检查: 运行预定义的数据质量测试（例如，“user_id列的空值率必须低于1%”）。这些规则可以按数据集进行配置。

血缘检查: 如1.4节所述，系统查询血缘图，确保新版本仅派生自受信任的、生产级别的上游数据源。

人工审查 (Human Review) (可选): 如果自动化检查通过，请求可以被路由到指定的负责人或审查委员会（例如，“数据治理委员会”）。他们可以审查数据集的文档、数据概况以及与前一版本的变更差异。

批准与提升 (Approval & Promotion): 一旦获得批准（无论是自动还是手动），系统将执行最终操作：以原子方式将 production 别名从旧版本更新到新的 v3.0.0 版本。

通知 (Notification): 系统向订阅了该数据集的用户和团队发送通知，告知他们新的生产版本已可用。

将数据集发布流程建模为“数据的拉取请求”，可以利用一个非常成功的协作模式，并将其直接应用于数据治理 。这个类比为UI设计提供了清晰的指引：一个“待处理发布请求”的仪表盘、一个可以“比较”不同数据集版本的视图、一个供审查者使用的评论区，以及一个明确的“批准”按钮。这种设计不仅功能强大，而且对于系统的主要技术用户来说，其心智模型是自然且熟悉的。   

